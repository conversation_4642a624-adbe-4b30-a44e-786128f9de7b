import { useState, useEffect } from "react";
import { Refresh<PERSON><PERSON>, Clock, <PERSON>, Building2, TrendingUp, Calendar } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import StatusBadge from "@/components/StatusBadge";
import { cn } from "@/lib/utils";
import officesData from "@/mockData/offices.json";
import officesUsualLoad from "@/mockData/officesUsualLoad.json";

const getWaitTimeStatus = (minutes: number) => {
  if (minutes === 0) return "low";
  if (minutes <= 10) return "low";
  if (minutes <= 30) return "moderate";
  return "high";
};

interface UsualOffice {
  id: string;
  name: string;
  days: Record<string, DayData[]>;
  best_visit_times: BestVisitTime[];
}

interface DayData {
  time: string;
  usual_wait_time: number;
  usual_queue_length: number;
}

interface BestVisitTime {
  time: string;
  reason: string;
}

interface Service {
  service_id: number;
  service_name: string;
  queue_length: number;
  wait_time_minutes: number;
  active_counters: number;
  is_active: boolean;
}

interface OfficeData {
  office_id: string;
  office_name: string;
  address: string;
  is_open: boolean;
  services: Service[];
}

interface OfficeResponse {
  status: string;
  data: OfficeData;
  timestamp: string;
}

interface OfficesData {
  [key: string]: OfficeResponse;
}

const Offices = () => {
  const [usualLoad] = useState(officesUsualLoad.data);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedOffice, setSelectedOffice] = useState("radnice");
  const [selectedDay, setSelectedDay] = useState(() => {
    const today = new Date().toLocaleDateString('cs-CZ', { weekday: 'long' });
    return today;
  });

  const currentOffice = (officesData as OfficesData)[selectedOffice]?.data;
  const currentOfficeUsualLoad = usualLoad.offices.find((o: UsualOffice) => o.id === selectedOffice);

  // Check if today is the current day to show real-time overlay
  const isCurrentDay = () => {
    const today = new Date().toLocaleDateString('cs-CZ', { weekday: 'long' });
    return today === selectedDay;
  };

  // Prepare chart data with current day overlay if applicable
  const getChartData = () => {
    const usualData = currentOfficeUsualLoad?.days?.[selectedDay] || [];

    if (!isCurrentDay() || !usualLoad.current_day_data?.enabled) {
      return usualData;
    }

    // Get all current day data points for the selected office
    const currentDayData = usualLoad.current_day_data.current_time_data.filter(
      item => item.office_id === selectedOffice
    );

    if (!currentDayData || currentDayData.length === 0) {
      return usualData;
    }

    // Create a map of current data by time for quick lookup
    const currentDataMap = new Map();
    currentDayData.forEach(item => {
      currentDataMap.set(item.time, item);
    });

    // Merge usual data with current day data
    const enhancedData = usualData.map(point => {
      const currentData = currentDataMap.get(point.time);
      if (currentData) {
        return {
          ...point,
          current_wait_time: currentData.current_wait_time,
          current_queue_length: currentData.current_queue_length
        };
      }
      return point;
    });

    // Add any current day data points that don't exist in usual data
    currentDayData.forEach(currentData => {
      const timeExists = usualData.some(point => point.time === currentData.time);
      if (!timeExists) {
        enhancedData.push({
          time: currentData.time,
          usual_wait_time: 0, // We don't have usual data for this exact time
          usual_queue_length: 0,
          current_wait_time: currentData.current_wait_time,
          current_queue_length: currentData.current_queue_length
        });
      }
    });

    // Sort by time
    enhancedData.sort((a, b) => a.time.localeCompare(b.time));

    return enhancedData;
  };

  // Ensure selected day exists for the current office (handles weekends or missing days)
  useEffect(() => {
    const days = currentOfficeUsualLoad?.days ? Object.keys(currentOfficeUsualLoad.days) : [];
    if (days.length) {
      const today = new Date().toLocaleDateString('cs-CZ', { weekday: 'long' });
      const next = days.includes(today) ? today : days[0];
      if (!days.includes(selectedDay)) {
        setSelectedDay(next);
      }
    }
  }, [selectedOffice, currentOfficeUsualLoad, selectedDay]);

  const refreshData = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsRefreshing(false);
  };

  useEffect(() => {
    const interval = setInterval(refreshData, 300000); // 5 minutes
    return () => clearInterval(interval);
  }, []);

  const totalWaitingPeople = currentOffice?.services.reduce((total, service) => total + service.queue_length, 0) || 0;
  const averageWaitTime = currentOffice?.services.length > 0 
    ? Math.round(currentOffice.services.reduce((total, service) => total + service.wait_time_minutes, 0) / currentOffice.services.length)
    : 0;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Úřady České Budějovice
            </h1>
            <p className="text-muted-foreground">
              Aktuální fronty a čekací doby na úřadech
            </p>
          </div>
          <Button
            onClick={refreshData}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Obnovit
          </Button>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          Poslední aktualizace: {lastUpdated.toLocaleTimeString("cs-CZ")}
        </div>
      </div>

      {/* Office Selector */}
      <Card className="mb-8 shadow-civic">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-primary rounded-lg">
                <Building2 className="h-8 w-8 text-primary-foreground" />
              </div>
              <div>
                <CardTitle className="text-2xl mb-1">Výběr úřadu</CardTitle>
                <p className="text-muted-foreground">Vyberte úřad pro zobrazení detailů</p>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Select value={selectedOffice} onValueChange={setSelectedOffice}>
            <SelectTrigger className="w-full md:w-80">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="radnice">Matriční úřad, ohlašovna pobytu</SelectItem>
              <SelectItem value="jeronymova">Oddělení EO, OP, CD a ŘP</SelectItem>
              <SelectItem value="knezska">Oddělení evidence motorových vozidel</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {currentOffice && (
        <>
          {/* Main Stats Card */}
          <Card className="mb-8 shadow-civic">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CardTitle className="text-2xl">{currentOffice.office_name}</CardTitle>
                  <StatusBadge status={currentOffice.is_open ? "low" : "closed"}>
                    {currentOffice.is_open ? "Otevřeno" : "Zavřeno"}
                  </StatusBadge>
                </div>
                <div className="text-right">
                  <div className="text-4xl font-bold text-primary mb-1">
                    {totalWaitingPeople}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    lidí čeká celkem
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Users className="h-6 w-6 mx-auto mb-2 text-primary" />
                  <div className="text-2xl font-bold text-foreground">{currentOffice.services.length}</div>
                  <div className="text-sm text-muted-foreground">Služeb</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Clock className="h-6 w-6 mx-auto mb-2 text-secondary" />
                  <div className="text-2xl font-bold text-foreground">{averageWaitTime} min</div>
                  <div className="text-sm text-muted-foreground">Průměrné čekání</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-foreground">{totalWaitingPeople}</div>
                  <div className="text-sm text-muted-foreground">Čekající lidé</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-success">
                    {currentOffice.services.reduce((total, service) => total + service.active_counters, 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">Aktivní přepážky</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {currentOffice.services.map((service) => (
              <Card key={service.service_id} className="shadow-civic hover:shadow-lg transition-smooth">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg font-semibold leading-tight">
                      {service.service_name}
                    </CardTitle>
                    <StatusBadge status={getWaitTimeStatus(service.wait_time_minutes)}>
                      {service.wait_time_minutes === 0 ? "Bez čekání" : `${service.wait_time_minutes} min`}
                    </StatusBadge>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Ve frontě:</span>
                      </div>
                      <span className="font-medium">
                        {service.queue_length} {service.queue_length === 1 ? "osoba" : 
                         service.queue_length < 5 ? "osoby" : "osob"}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Aktivní přepážky:</span>
                      <span className="font-medium text-primary">
                        {service.active_counters}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Stav služby:</span>
                      <span className={cn(
                        "text-sm font-medium",
                        service.is_active ? "text-success" : "text-muted-foreground"
                      )}>
                        {service.is_active ? "Aktivní" : "Neaktivní"}
                      </span>
                    </div>

                    <div className="pt-2">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-xs text-muted-foreground">Čekací doba</span>
                        <span className="text-xs text-muted-foreground">
                          {service.wait_time_minutes > 30 ? "Vysoká" : 
                           service.wait_time_minutes > 10 ? "Střední" : 
                           service.wait_time_minutes > 0 ? "Nízká" : "Žádná"}
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-1.5">
                        <div
                          className={cn(
                            "h-1.5 rounded-full transition-smooth",
                            service.wait_time_minutes > 30 ? "bg-destructive" :
                            service.wait_time_minutes > 10 ? "bg-warning" :
                            service.wait_time_minutes > 0 ? "bg-success" :
                            "bg-success"
                          )}
                          style={{ 
                            width: `${Math.min(100, (service.wait_time_minutes / 60) * 100)}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Usual Load Data */}
          <Card className="mb-8 shadow-civic">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Obvyklé vytížení
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex flex-col md:flex-row gap-4">
                <Select value={selectedOffice} onValueChange={setSelectedOffice}>
                  <SelectTrigger className="w-full md:w-80">
                    <SelectValue placeholder="Vyberte úřad" />
                  </SelectTrigger>
                  <SelectContent>
                    {usualLoad.offices.map((office: UsualOffice) => (
                      <SelectItem key={office.id} value={office.id}>
                        {office.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedDay} onValueChange={setSelectedDay}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {currentOfficeUsualLoad?.days && Object.keys(currentOfficeUsualLoad.days).map((day) => (
                      <SelectItem key={day} value={day}>
                        {day.charAt(0).toUpperCase() + day.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="h-80 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={getChartData()}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis
                      dataKey="time"
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                      tickFormatter={(value) => `${value} min`}
                    />
                    <Tooltip
                      formatter={(value: number, name) => [
                        `${value} ${name === 'usual_wait_time' ? 'minut' :
                        name === 'current_wait_time' ? 'minut' : 'osob'}`,
                        name === 'usual_wait_time' ? 'Obvyklá čekací doba' :
                        name === 'current_wait_time' ? 'Aktuální čekací doba' : 'Délka fronty'
                      ]}
                      labelFormatter={(label) => `Čas: ${label}`}
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="usual_wait_time"
                      stroke="hsl(var(--primary))"
                      strokeWidth={3}
                      dot={{ r: 4, fill: 'hsl(var(--primary))' }}
                      activeDot={{ r: 6, fill: 'hsl(var(--primary))' }}
                      name="usual_wait_time"
                    />
                    {isCurrentDay() && usualLoad.current_day_data?.enabled && (
                      <Line
                        type="monotone"
                        dataKey="current_wait_time"
                        stroke="hsl(var(--destructive))"
                        strokeWidth={3}
                        strokeDasharray="5 5"
                        dot={{ r: 4, fill: 'hsl(var(--destructive))' }}
                        activeDot={{ r: 6, fill: 'hsl(var(--destructive))' }}
                        name="current_wait_time"
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {isCurrentDay() && (
                <div className="mt-4 p-3 bg-info-light rounded-lg border border-info/20">
                  <h5 className="font-medium text-info mb-1">Aktuální den</h5>
                  <p className="text-sm text-info/80">
                    Červená čárkovaná čára zobrazuje aktuální čekací dobu pro dnešní den ve srovnání s obvyklými hodnotami.
                  </p>
                </div>
              )}

              {/* Best Visit Times */}
              <div className="mt-4 space-y-3">
                <h4 className="font-semibold">Doporučené časy návštěvy:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {currentOfficeUsualLoad?.best_visit_times?.map((time, index) => (
                    <div key={index} className="p-3 bg-success-light rounded-lg border border-success/20">
                      <div className="flex items-center gap-2 mb-1">
                        <Calendar className="h-4 w-4 text-success" />
                        <span className="font-medium text-success">{time.time}</span>
                      </div>
                      <p className="text-sm text-success/80">{time.reason}</p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default Offices;