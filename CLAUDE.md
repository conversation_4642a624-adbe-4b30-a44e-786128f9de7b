# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Budejo Pulse is a modern React TypeScript application for monitoring office occupancy, parking availability, and pool usage. It features real-time data visualization, predictive analytics, and a responsive design with dark/light mode support.

## Development Commands

### Core Commands
- `npm run dev` - Start development server on port 8080
- `npm run build` - Create production build
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint linter
- `npm run type-check` - Run TypeScript compiler check
- `npm test` - Run tests with Vitest

### Environment Management
This project uses [mise](https://mise.jdx.dev/) for environment management. Run `mise install` to set up the correct Node.js version (22+) and npm automatically.

## Architecture

### Tech Stack
- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **Routing**: React Router DOM (client-side SPA)
- **State Management**: TanStack Query (React Query) for server state
- **Charts**: Recharts for data visualization
- **Icons**: Lucide React

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── Navigation.tsx  # Main navigation component
│   └── StatusBadge.tsx # Status indicator component
├── pages/              # Route components
│   ├── Parking.tsx     # Parking management (root route "/")
│   ├── Offices.tsx     # Office occupancy ("/offices")
│   ├── Pool.tsx        # Pool usage ("/pool")
│   └── NotFound.tsx    # 404 page
├── mockData/           # JSON mock data files for development
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
├── App.tsx             # Main app component with routing
└── main.tsx            # Application entry point
```

### Key Conventions

#### TypeScript Configuration
- Uses path aliases: `@/` maps to `src/`
- TypeScript strict mode is partially disabled (noImplicitAny: false, strictNullChecks: false)
- ESLint configured with TypeScript support but unused vars checking disabled

#### Routing
- Single Page Application (SPA) using React Router
- Root route "/" redirects to Parking page
- Add new routes above the catch-all "*" route in App.tsx
- Navigation items configured in `src/components/Navigation.tsx`

#### Data Management
- Mock data stored in `src/mockData/` as JSON files
- TanStack Query used for data fetching and caching
- Data includes: parking history/predictions, office occupancy, pool usage

#### UI Components
- Uses shadcn/ui component library in `src/components/ui/`
- Tailwind CSS for styling with custom design tokens
- Responsive design with mobile-first approach
- Dark/light mode support via next-themes

#### Development Features
- Hot module replacement (HMR) with Vite
- Development server runs on `http://localhost:8080`
- Component tagging in development mode
- Path aliases for clean imports

### CI/CD
- GitHub Actions workflow in `.github/workflows/ci.yml`
- Runs lint, type-check, and build on PRs and main branch pushes
- Uses Node.js 22 in CI environment

## Testing

The project uses Vitest for testing. Test files should be placed in:
- `src/__tests__/` for general tests
- `src/components/__tests__/` for component tests
- `src/lib/__tests__/` for utility tests

## Important Notes

- Application text is in Czech (navigation labels: "Parkování", "Úřady", "Bazén")
- Development server listens on all interfaces ("::")
- TypeScript configuration is relaxed for faster development
- ESLint is configured but allows unused variables
- Mock data drives the application during development